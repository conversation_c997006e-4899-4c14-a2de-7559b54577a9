#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os

def detect_and_read_file(filename):
    """尝试不同编码读取文件"""

    # 尝试不同的编码
    encodings_to_try = [
        'utf-8',
        'gbk',
        'gb2312',
        'gb18030',
        'big5',
        'utf-16',
        'utf-16le',
        'utf-16be',
        'ascii',
        'latin1'
    ]
    
    for encoding in encodings_to_try:
        try:
            print(f"\n尝试编码: {encoding}")
            with open(filename, 'r', encoding=encoding) as f:
                lines = []
                for i, line in enumerate(f):
                    if i >= 10:  # 只读前10行
                        break
                    lines.append(line.strip())
                
                print("成功读取内容:")
                for i, line in enumerate(lines, 1):
                    print(f"{i:2d}: {line}")
                
                return encoding, lines
                
        except Exception as e:
            print(f"编码 {encoding} 失败: {e}")
    
    print("所有编码都失败了")
    return None, None

if __name__ == "__main__":
    filename = "遮天.txt"
    if os.path.exists(filename):
        encoding, content = detect_and_read_file(filename)
        if encoding:
            print(f"\n推荐使用编码: {encoding}")
    else:
        print(f"文件 {filename} 不存在")
